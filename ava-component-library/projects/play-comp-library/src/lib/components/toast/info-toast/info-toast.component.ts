import { 
    ChangeDetectionStrategy, 
    Component, 
    Input, 
    Output, 
    EventEmitter,
    OnInit,
    OnDestroy
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../icon/icon.component';
import { ButtonComponent } from '../../button/button.component';
import { ToastResult, ToastTheme } from '../toast-service';

@Component({
    selector: 'ava-info-toast',
    imports: [CommonModule, IconComponent, ButtonComponent],
    templateUrl: './info-toast.component.html',
    styleUrl: './info-toast.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class InfoToastComponent implements OnInit, OnDestroy {
    @Input() title = 'Information';
    @Input() message = 'Here is some important information for you.';
    @Input() icon = 'info';
    @Input() iconColor = 'var(--color-text-info)';
    @Input() iconSize = 24;
    @Input() theme: ToastTheme = 'light';
    @Input() duration = 4000;
    @Input() closable = true;
    @Input() showProgress = true;
    @Input() showLearnMoreButton = false;
    @Input() learnMoreButtonText = 'Learn More';

    @Output() closed = new EventEmitter<ToastResult>();

    isVisible = false;
    isExiting = false;
    progressWidth = 0;
    private progressTimer?: number;

    ngOnInit() {
        // Trigger entrance animation
        setTimeout(() => {
            this.isVisible = true;
        }, 10);

        // Start progress bar animation if enabled
        if (this.showProgress && this.duration > 0) {
            this.startProgressAnimation();
        }
    }

    ngOnDestroy() {
        if (this.progressTimer) {
            clearInterval(this.progressTimer);
        }
    }

    private startProgressAnimation() {
        const interval = 50; // Update every 50ms
        const increment = (interval / this.duration) * 100;
        
        this.progressTimer = window.setInterval(() => {
            this.progressWidth += increment;
            if (this.progressWidth >= 100) {
                this.progressWidth = 100;
                if (this.progressTimer) {
                    clearInterval(this.progressTimer);
                }
            }
        }, interval);
    }

    startExitAnimation() {
        this.isExiting = true;
    }

    onClose() {
        this.closed.emit({ action: 'close' });
    }

    onLearnMore() {
        this.closed.emit({ action: 'learn-more' });
    }

    onToastClick() {
        // Optional: Handle toast click
    }
}
