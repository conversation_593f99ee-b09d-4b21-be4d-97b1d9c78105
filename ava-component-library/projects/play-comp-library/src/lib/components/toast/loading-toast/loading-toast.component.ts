import { 
    ChangeDetectionStrategy, 
    Component, 
    Input, 
    Output, 
    EventEmitter,
    OnInit,
    OnDestroy
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../icon/icon.component';
import { ButtonComponent } from '../../button/button.component';
import { ToastResult, ToastTheme } from '../toast-service';

@Component({
    selector: 'ava-loading-toast',
    imports: [CommonModule, IconComponent, ButtonComponent],
    templateUrl: './loading-toast.component.html',
    styleUrl: './loading-toast.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class LoadingToastComponent implements OnInit, OnDestroy {
    @Input() title = 'Loading...';
    @Input() message = 'Please wait while we process your request.';
    @Input() theme: ToastTheme = 'light';
    @Input() duration = 0; // Loading toasts don't auto-dismiss by default
    @Input() closable = false; // Loading toasts usually aren't closable
    @Input() showProgress = false; // Loading toasts use spinner instead
    @Input() progress = 0;
    @Input() indeterminate = true;
    @Input() showCancelButton = false;
    @Input() cancelButtonText = 'Cancel';

    @Output() closed = new EventEmitter<ToastResult>();

    isVisible = false;
    isExiting = false;

    ngOnInit() {
        // Trigger entrance animation
        setTimeout(() => {
            this.isVisible = true;
        }, 10);
    }

    ngOnDestroy() {
        // Cleanup if needed
    }

    startExitAnimation() {
        this.isExiting = true;
    }

    onClose() {
        this.closed.emit({ action: 'close' });
    }

    onCancel() {
        this.closed.emit({ action: 'cancel' });
    }

    onToastClick() {
        // Optional: Handle toast click
    }
}
