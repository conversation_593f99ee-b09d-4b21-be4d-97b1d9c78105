<div class="ava-toast loading"
     [ngClass]="{
         'show': isVisible,
         'hide': isExiting,
         'theme-light': theme === 'light',
         'theme-dark': theme === 'dark'
     }"
     (click)="onToastClick()">
    
    <div class="toast-icon">
        <div class="spinner" [ngClass]="{ 'indeterminate': indeterminate }"></div>
    </div>
    
    <div class="toast-content">
        <div class="toast-title" *ngIf="title">{{ title }}</div>
        <div class="toast-message" *ngIf="message" [innerHTML]="message"></div>
        
        <!-- Progress bar for determinate loading -->
        <div class="progress-container" *ngIf="!indeterminate">
            <div class="progress-bar" [style.width.%]="progress"></div>
            <span class="progress-text">{{ progress }}%</span>
        </div>
        
        <div class="toast-actions" *ngIf="showCancelButton">
            <ava-button 
                [label]="cancelButtonText"
                variant="secondary"
                size="small"
                (userClick)="onCancel()">
            </ava-button>
        </div>
    </div>
    
    <ava-button class="toast-close"
                *ngIf="closable"
                variant="secondary"
                [clear]="true"
                size="small"
                iconPosition="only"
                iconName="x"
                [iconSize]="16"
                (userClick)="onClose()"
                aria-label="Close toast">
    </ava-button>
</div>
