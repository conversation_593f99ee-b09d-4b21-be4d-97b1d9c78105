@import '../success-toast/success-toast.component.scss';

/* Loading Toast Specific Styling */
.ava-toast.loading {
    background: var(--toast-loading-background);
    color: var(--toast-loading-text);
    border-color: var(--toast-loading-border);
}

/* Loading Spinner */
.spinner {
    width: var(--toast-spinner-width);
    height: var(--toast-spinner-height);
    border: var(--toast-spinner-border);
    border-radius: var(--toast-spinner-border-radius);
    border-top-color: var(--toast-spinner-border-top-color);
    animation: var(--toast-spinner-animation);
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Progress Container for Determinate Loading */
.progress-container {
    margin-top: var(--toast-loading-progress-margin-top);
    display: var(--toast-loading-progress-display);
    align-items: var(--toast-loading-progress-align-items);
    gap: var(--toast-loading-progress-gap);
}

.progress-bar {
    height: var(--toast-loading-progress-bar-height);
    background: var(--toast-loading-progress-bar-background);
    border-radius: var(--toast-loading-progress-bar-border-radius);
    transition: var(--toast-loading-progress-bar-transition);
    flex: var(--toast-loading-progress-bar-flex);
}

.progress-text {
    font-size: var(--toast-loading-progress-text-font-size);
    opacity: var(--toast-loading-progress-text-opacity);
    min-width: var(--toast-loading-progress-text-min-width);
    text-align: var(--toast-loading-progress-text-text-align);
}
