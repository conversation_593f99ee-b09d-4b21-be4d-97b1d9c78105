import { 
    ChangeDetectionStrategy, 
    Component, 
    Input, 
    Output, 
    EventEmitter,
    OnInit,
    OnDestroy
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../icon/icon.component';
import { ButtonComponent } from '../../button/button.component';
import { ToastResult, ToastTheme } from '../toast-service';

@Component({
    selector: 'ava-error-toast',
    imports: [CommonModule, IconComponent, ButtonComponent],
    templateUrl: './error-toast.component.html',
    styleUrl: './error-toast.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class ErrorToastComponent implements OnInit, OnDestroy {
    @Input() title = 'Error';
    @Input() message = 'An error occurred. Please try again.';
    @Input() icon = 'alert-circle';
    @Input() iconColor = 'var(--color-text-error)';
    @Input() iconSize = 24;
    @Input() theme: ToastTheme = 'light';
    @Input() duration = 4000;
    @Input() closable = true;
    @Input() showProgress = true;
    @Input() showRetryButton = false;
    @Input() retryButtonText = 'Retry';

    @Output() closed = new EventEmitter<ToastResult>();

    isVisible = false;
    isExiting = false;
    progressWidth = 0;
    private progressTimer?: number;

    ngOnInit() {
        // Trigger entrance animation
        setTimeout(() => {
            this.isVisible = true;
        }, 10);

        // Start progress bar animation if enabled
        if (this.showProgress && this.duration > 0) {
            this.startProgressAnimation();
        }
    }

    ngOnDestroy() {
        if (this.progressTimer) {
            clearInterval(this.progressTimer);
        }
    }

    private startProgressAnimation() {
        const interval = 50; // Update every 50ms
        const increment = (interval / this.duration) * 100;
        
        this.progressTimer = window.setInterval(() => {
            this.progressWidth += increment;
            if (this.progressWidth >= 100) {
                this.progressWidth = 100;
                if (this.progressTimer) {
                    clearInterval(this.progressTimer);
                }
            }
        }, interval);
    }

    startExitAnimation() {
        this.isExiting = true;
    }

    onClose() {
        this.closed.emit({ action: 'close' });
    }

    onRetry() {
        this.closed.emit({ action: 'retry' });
    }

    onToastClick() {
        // Optional: Handle toast click
    }
}
