.ava-toast {
    pointer-events: auto;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: var(--toast-padding);
    border-radius: var(--toast-border-radius);
    box-shadow: var(--toast-shadow);
    backdrop-filter: blur(10px);
    border: var(--toast-border);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(100px);
    opacity: 0;
    max-width: 100%;
    min-width: var(--toast-min-width);
    position: relative;
    overflow: hidden;
    cursor: pointer;
    margin-bottom: 8px;

    &.show {
        transform: translateY(0);
        opacity: 1;
    }

    &.hide {
        transform: translateY(-100px);
        opacity: 0;
        margin-top: -80px;
    }

    /* Success Toast Specific Styling */
    &.success {
        background: var(--toast-success-background);
        color: var(--toast-success-text);
        border-color: var(--toast-success-border);
    }

    /* Theme Variations */
    &.theme-light {
        background: var(--toast-background);
        color: var(--toast-title-color);
        border: 1px solid var(--toast-border);
    }

    &.theme-dark {
        background: var(--toast-background-dark);
        color: var(--toast-title-color-dark);
        border: 1px solid var(--toast-border);
    }
}

/* Toast Icon */
.toast-icon {
    width: 24px;
    height: 24px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Toast Content */
.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: var(--toast-title-weight);
    margin-bottom: var(--toast-title-margin-bottom);
    line-height: var(--toast-title-line-height);
    font-size: 0.95rem;
    color: var(--toast-title-color);
}

.toast-message {
    opacity: 0.9;
    font-size: 0.85rem;
    line-height: var(--toast-message-line-height);
    color: var(--toast-message-color);
}

/* Close Button */
.toast-close {
    flex-shrink: 0;
}

/* Progress Bar */
.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 0 0 var(--toast-border-radius) var(--toast-border-radius);
    transition: width linear;
}

/* Toast Actions */
.toast-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

/* Responsive */
@media (max-width: 768px) {
    .ava-toast {
        min-width: var(--toast-mobile-min-width);
        width: var(--toast-mobile-width);
    }
}
