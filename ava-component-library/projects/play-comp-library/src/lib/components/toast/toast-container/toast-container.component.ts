import {
    ChangeDetectionStrategy,
    Component,
    ViewChild,
    ViewContainerRef,
    ViewEncapsulation,
    Input
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ToastPosition } from '../toast-service';

@Component({
    selector: 'ava-toast-container',
    imports: [CommonModule],
    templateUrl: './toast-container.component.html',
    styleUrl: './toast-container.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    encapsulation: ViewEncapsulation.None
})
export class ToastContainerComponent {
    @ViewChild('container', { read: ViewContainerRef, static: true })
    container!: ViewContainerRef;

    @Input() position: ToastPosition = 'top-right';

    setPosition(position: ToastPosition) {
        this.position = position;
    }
}
