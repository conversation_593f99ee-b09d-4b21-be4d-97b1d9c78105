/* Toast Container */
.ava-toast-container {
    position: var(--toast-container-position);
    z-index: var(--toast-container-z-index);
    pointer-events: none;
    display: flex;
    flex-direction: column;
    gap: var(--toast-container-gap);
    max-width: var(--toast-container-max-width);
    width: 100%;
}

.ava-toast-container.position-top-left {
    top: var(--toast-container-top);
    left: var(--toast-container-top);
}

.ava-toast-container.position-top-center {
    top: var(--toast-container-top);
    left: 50%;
    transform: translateX(-50%);
}

.ava-toast-container.position-top-right {
    top: var(--toast-container-top);
    right: var(--toast-container-right);
}

.ava-toast-container.position-bottom-left {
    bottom: var(--toast-container-top);
    left: var(--toast-container-top);
}

.ava-toast-container.position-bottom-center {
    bottom: var(--toast-container-top);
    left: 50%;
    transform: translateX(-50%);
}

.ava-toast-container.position-bottom-right {
    bottom: var(--toast-container-top);
    right: var(--toast-container-right);
}

/* Responsive */
@media (max-width: 768px) {
    .ava-toast-container {
        max-width: var(--toast-mobile-max-width);
        left: var(--toast-mobile-left) !important;
        right: var(--toast-mobile-right) !important;
        transform: var(--toast-mobile-transform) !important;
    }
}
