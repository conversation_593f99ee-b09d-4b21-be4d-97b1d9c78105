<div class="ava-toast warning"
     [ngClass]="{
         'show': isVisible,
         'hide': isExiting,
         'theme-light': theme === 'light',
         'theme-dark': theme === 'dark'
     }"
     (click)="onToastClick()">
    
    <div class="toast-icon">
        <ava-icon 
            [iconName]="icon"
            [iconSize]="iconSize"
            [iconColor]="iconColor"
            aria-hidden="true">
        </ava-icon>
    </div>
    
    <div class="toast-content">
        <div class="toast-title" *ngIf="title">{{ title }}</div>
        <div class="toast-message" *ngIf="message" [innerHTML]="message"></div>
        
        <div class="toast-actions" *ngIf="showActionButton">
            <ava-button 
                [label]="actionButtonText"
                variant="warning"
                size="small"
                (userClick)="onAction()">
            </ava-button>
        </div>
    </div>
    
    <ava-button class="toast-close"
                *ngIf="closable"
                variant="secondary"
                [clear]="true"
                size="small"
                iconPosition="only"
                iconName="x"
                [iconSize]="16"
                (userClick)="onClose()"
                aria-label="Close toast">
    </ava-button>
    
    <div class="toast-progress" 
         *ngIf="showProgress && duration > 0"
         [style.width.%]="progressWidth">
    </div>
</div>
