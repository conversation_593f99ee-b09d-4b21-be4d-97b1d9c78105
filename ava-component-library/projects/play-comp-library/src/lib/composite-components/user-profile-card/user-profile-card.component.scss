.user-profile-card {
  --profile-card-bg: #ffffff;
  --profile-card-border: #e9ecef;
  --profile-card-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --profile-text-primary: #212529;
  --profile-text-secondary: #6c757d;
  --profile-text-muted: #adb5bd;
  --profile-border-radius: 8px;
  --profile-spacing: 16px;
  --profile-avatar-size: 80px;
  --profile-status-size: 20px;
  --profile-transition: all 0.2s ease-in-out;

  display: block;
  transition: var(--profile-transition);

  // Theme Variations
  &.theme-minimal {
    --profile-card-shadow: none;
    --profile-border-radius: 4px;
    --profile-spacing: 12px;
  }

  &.theme-modern {
    --profile-card-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    --profile-border-radius: 12px;
    --profile-spacing: 20px;
  }

  &.theme-professional {
    --profile-card-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
    --profile-border-radius: 6px;
    --profile-spacing: 18px;
  }

  // Layout Variations
  &.layout-horizontal {
    .profile-header {
      flex-direction: row;
      align-items: flex-start;
      gap: var(--profile-spacing);
    }

    .profile-info {
      flex-direction: row;
      align-items: center;
      gap: var(--profile-spacing);
    }

    .avatar-section {
      flex-shrink: 0;
    }

    .basic-info {
      flex: 1;
    }
  }

  &.layout-compact {
    --profile-spacing: 12px;
    --profile-avatar-size: 60px;

    .profile-header {
      padding: var(--profile-spacing);
    }

    .profile-content {
      padding: 0 var(--profile-spacing) var(--profile-spacing);
    }

    .section-title {
      font-size: 0.875rem;
      margin-bottom: 8px;
    }
  }

  // States
  &.loading {
    opacity: 0.7;
    pointer-events: none;
  }

  &.disabled {
    opacity: 0.5;
    pointer-events: none;
  }

  // Profile Header
  .profile-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: var(--profile-spacing);
    border-bottom: 1px solid var(--profile-card-border);
  }

  .profile-info {
    display: flex;
    flex-direction: column;
    gap: var(--profile-spacing);
    flex: 1;
  }

  .avatar-section {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }

  .profile-avatar {
    cursor: pointer;
    transition: var(--profile-transition);

    &.clickable:hover {
      transform: scale(1.05);
    }
  }

  .status-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    font-size: 0.75rem;
    color: var(--profile-text-secondary);
    cursor: pointer;
    transition: var(--profile-transition);

    &:hover {
      background: rgba(255, 255, 255, 1);
      transform: translateY(-1px);
    }
  }

  .status-text {
    font-weight: 500;
  }

  .basic-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .user-name {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--profile-text-primary);
    cursor: pointer;
    transition: var(--profile-transition);

    &:hover {
      color: #007bff;
    }
  }

  .user-role,
  .user-department,
  .user-location {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.875rem;
    color: var(--profile-text-secondary);
  }

  .header-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
  }

  // Profile Content
  .profile-content {
    padding: var(--profile-spacing);
    display: flex;
    flex-direction: column;
    gap: var(--profile-spacing);
  }

  .section-title {
    margin: 0 0 12px 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--profile-text-primary);
  }

  // Contact Information
  .contact-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .contact-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--profile-transition);

    &:hover {
      background: #e9ecef;
    }
  }

  .contact-value {
    font-size: 0.875rem;
    color: var(--profile-text-primary);
  }

  // Bio Section
  .bio-section {
    .bio-text {
      margin: 0 0 8px 0;
      font-size: 0.875rem;
      line-height: 1.5;
      color: var(--profile-text-secondary);
    }

    .expand-bio {
      background: none;
      border: none;
      color: #007bff;
      font-size: 0.875rem;
      cursor: pointer;
      padding: 0;
      text-decoration: underline;

      &:hover {
        color: #0056b3;
      }
    }
  }

  // Skills Section
  .skills-section {
    .skills-list {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
    }
  }

  // Stats Section
  .stats-section {
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
      gap: 12px;
    }

    .stat-item {
      text-align: center;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 6px;
      transition: var(--profile-transition);

      &:hover {
        background: #e9ecef;
      }
    }

    .stat-value {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--profile-text-primary);
      line-height: 1;
    }

    .stat-label {
      font-size: 0.75rem;
      color: var(--profile-text-secondary);
      margin-top: 4px;
    }
  }

  // Social Links
  .social-section {
    .social-links {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  }

  // Expanded Info
  .expanded-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #007bff;
  }

  .info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.875rem;
    color: var(--profile-text-secondary);
  }

  // Actions Section
  .actions-section {
    .actions-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    --profile-spacing: 12px;
    --profile-avatar-size: 60px;

    &.layout-horizontal {
      .profile-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
      }

      .profile-info {
        flex-direction: column;
        align-items: center;
      }

      .basic-info {
        text-align: center;
      }
    }

    .header-actions {
      margin-top: 12px;
    }

    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .social-links {
      justify-content: center;
    }

    .actions-grid {
      justify-content: center;
    }
  }

  @media (max-width: 480px) {
    --profile-spacing: 10px;

    .profile-header {
      padding: var(--profile-spacing);
    }

    .profile-content {
      padding: var(--profile-spacing);
    }

    .user-name {
      font-size: 1.125rem;
    }

    .stats-grid {
      grid-template-columns: 1fr;
    }
  }

  // Loading State
  &.loading {
    .profile-avatar,
    .user-name,
    .contact-item,
    .bio-text,
    .skills-list,
    .stats-grid {
      position: relative;
      overflow: hidden;

      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.4),
          transparent
        );
        animation: loading-shimmer 1.5s infinite;
      }
    }
  }

  @keyframes loading-shimmer {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  // Focus States for Accessibility
  .user-name:focus,
  .contact-item:focus,
  .expand-bio:focus,
  .status-indicator:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
  }

  // Keyboard Navigation
  .user-name,
  .contact-item,
  .expand-bio,
  .status-indicator {
    &:focus-visible {
      outline: 2px solid #007bff;
      outline-offset: 2px;
    }
  }
}
