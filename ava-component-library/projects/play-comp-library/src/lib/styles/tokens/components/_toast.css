/**
 * =========================================================================
 * Play+ Design System: Toast Component Tokens
 *
 * Component-specific semantic tokens for toast notification elements.
 * These tokens reference base semantic tokens and provide
 * contextual meaning for toast styling.
 * =========================================================================
 */

:root {
  /* --- Toast Base --- */
  --toast-background: var(--color-background-primary);
  --toast-background-dark: var(--global-color-senary);
  --toast-border: var(--color-border-default);
  --toast-border-radius: var(--global-radius-md);
  --toast-shadow: var(--global-elevation-02);
  --toast-padding: var(--global-spacing-4);
  --toast-max-width: 400px;
  --toast-min-width: 300px;

  /* --- Toast Title --- */
  --toast-title-font: var(--font-body-2);
  --toast-title-font-dark: var(--font-body-1);
  --toast-title-color: var(--color-text-primary);
  --toast-title-color-dark: var(--color-text-on-brand);
  --toast-title-weight: var(--global-font-weight-semibold);
  --toast-title-line-height: var(--global-line-height-default);
  --toast-title-margin-bottom: var(--global-spacing-2);

  /* --- Toast Message --- */
  --toast-message-font: var(--font-body-2);
  --toast-message-font-dark: var(--font-body-2);
  --toast-message-color: var(--color-text-secondary);
  --toast-message-color-dark: var(--color-text-secondary);
  --toast-message-weight: var(--global-font-weight-regular);
  --toast-message-line-height: var(--global-line-height-default);
  --toast-message-margin-bottom: var(--global-spacing-3);

  /* --- Toast Link --- */
  --toast-link-font: var(--font-body-2);
  --toast-link-color: var(--color-text-interactive);
  --toast-link-color-hover: var(--color-text-interactive-hover);
  --toast-link-weight: var(--global-font-weight-medium);
  --toast-link-decoration: underline;
  --toast-link-decoration-hover: none;
  --toast-link-transition: var(--motion-pattern-fade);

  /* --- Toast Variants --- */
  --toast-success-background: var(--global-color-green-500);
  --toast-success-text: var(--color-text-on-brand);
  --toast-success-border: var(--global-color-green-500);

  --toast-error-background: var(--global-color-red-500);
  --toast-error-text: var(--color-text-on-brand);
  --toast-error-border: var(--global-color-red-500);

  --toast-warning-background: var(--global-color-yellow-500);
  --toast-warning-text: var(--global-color-black);
  --toast-warning-border: var(--global-color-yellow-500);

  --toast-info-background: var(--global-color-blue-info-500);
  --toast-info-text: var(--color-text-on-brand);
  --toast-info-border: var(--global-color-blue-info-500);

  /* --- Toast Sizes --- */
  --toast-size-sm-padding: var(--global-spacing-3);
  --toast-size-sm-font: var(--font-label);
  --toast-size-sm-max-width: 300px;

  --toast-size-md-padding: var(--global-spacing-4);
  --toast-size-md-font: var(--font-body-2);
  --toast-size-md-max-width: 400px;

  --toast-size-lg-padding: var(--global-spacing-5);
  --toast-size-lg-font: var(--font-body-1);
  --toast-size-lg-max-width: 500px;

  /* --- Toast Animation --- */
  --toast-transition: var(--motion-pattern-slide);
  --toast-animation-duration: var(--global-motion-duration-standard);
  --toast-animation-easing: var(--global-motion-easing-enter);

  /* --- Toast Container --- */
  --toast-container-position: fixed;
  --toast-container-top: var(--global-spacing-4);
  --toast-container-right: var(--global-spacing-4);
  --toast-container-z-index: 9999;
  --toast-container-gap: var(--global-spacing-3);
  --toast-container-max-width: 400px;

  /* --- Toast Layout --- */
  --toast-display: flex;
  --toast-align-items: center;
  --toast-gap: var(--global-spacing-3);
  --toast-margin-bottom: var(--global-spacing-2);
  --toast-position: relative;
  --toast-overflow: hidden;
  --toast-cursor: pointer;
  --toast-pointer-events: auto;

  /* --- Toast Icon --- */
  --toast-icon-width: 24px;
  --toast-icon-height: 24px;
  --toast-icon-flex-shrink: 0;

  /* --- Toast Content --- */
  --toast-content-flex: 1;

  /* --- Toast Close Button --- */
  --toast-close-background: none;
  --toast-close-border: none;
  --toast-close-color: currentColor;
  --toast-close-cursor: pointer;
  --toast-close-padding: var(--global-spacing-1);
  --toast-close-border-radius: var(--global-radius-sm);
  --toast-close-opacity: 0.7;
  --toast-close-opacity-hover: 1;
  --toast-close-transition: opacity 0.2s ease;
  --toast-close-flex-shrink: 0;
  --toast-close-font-size: 18px;
  --toast-close-line-height: 1;
  --toast-close-width: 24px;
  --toast-close-height: 24px;
  --toast-close-background-hover: rgba(255, 255, 255, 0.1);

  /* --- Toast Progress Bar --- */
  --toast-progress-position: absolute;
  --toast-progress-bottom: 0;
  --toast-progress-left: 0;
  --toast-progress-height: 3px;
  --toast-progress-background: rgba(255, 255, 255, 0.3);
  --toast-progress-border-radius: 0 0 var(--toast-border-radius) var(--toast-border-radius);
  --toast-progress-transition: width linear;

  /* --- Toast Actions --- */
  --toast-actions-display: flex;
  --toast-actions-gap: var(--global-spacing-2);
  --toast-actions-margin-top: var(--global-spacing-2);

  /* --- Toast Loading Spinner --- */
  --toast-spinner-width: 20px;
  --toast-spinner-height: 20px;
  --toast-spinner-border: 2px solid rgba(255, 255, 255, 0.3);
  --toast-spinner-border-radius: 50%;
  --toast-spinner-border-top-color: currentColor;
  --toast-spinner-animation: spin 1s linear infinite;

  /* --- Toast Loading Progress --- */
  --toast-loading-progress-margin-top: var(--global-spacing-2);
  --toast-loading-progress-display: flex;
  --toast-loading-progress-align-items: center;
  --toast-loading-progress-gap: var(--global-spacing-2);
  --toast-loading-progress-bar-height: 4px;
  --toast-loading-progress-bar-background: rgba(255, 255, 255, 0.8);
  --toast-loading-progress-bar-border-radius: 2px;
  --toast-loading-progress-bar-transition: width 0.3s ease;
  --toast-loading-progress-bar-flex: 1;
  --toast-loading-progress-text-font-size: 0.75rem;
  --toast-loading-progress-text-opacity: 0.9;
  --toast-loading-progress-text-min-width: 35px;
  --toast-loading-progress-text-text-align: right;

  /* --- Toast Animations --- */
  --toast-transform-enter: translateY(100px);
  --toast-transform-show: translateY(0);
  --toast-transform-exit: translateY(-100px);
  --toast-opacity-enter: 0;
  --toast-opacity-show: 1;
  --toast-opacity-exit: 0;
  --toast-margin-top-exit: -80px;

  /* --- Toast Theme Variants --- */
  /* Light Theme */
  --toast-theme-light-background: rgba(255, 255, 255, 0.95);
  --toast-theme-light-color: #374151;
  --toast-theme-light-border: 1px solid rgba(0, 0, 0, 0.1);

  /* Dark Theme */
  --toast-theme-dark-background: rgba(31, 41, 55, 0.95);
  --toast-theme-dark-color: #f9fafb;
  --toast-theme-dark-border: 1px solid rgba(255, 255, 255, 0.1);

  /* Minimal Theme */
  --toast-theme-minimal-background: rgba(255, 255, 255, 0.98);
  --toast-theme-minimal-color: #374151;
  --toast-theme-minimal-border: 1px solid #e5e7eb;
  --toast-theme-minimal-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  /* Glass Theme */
  --toast-theme-glass-background: rgba(255, 255, 255, 0.1);
  --toast-theme-glass-backdrop-filter: blur(20px);
  --toast-theme-glass-border: 1px solid rgba(255, 255, 255, 0.3);
  --toast-theme-glass-color: #1f2937;

  /* --- Toast Loading Variant --- */
  --toast-loading-background: linear-gradient(135deg, rgba(99, 102, 241, 0.9), rgba(79, 70, 229, 0.9));
  --toast-loading-text: var(--color-text-on-brand);
  --toast-loading-border: var(--global-color-blue-500);

  /* --- Responsive --- */
  --toast-mobile-max-width: calc(100vw - 40px);
  --toast-mobile-left: 20px;
  --toast-mobile-right: 20px;
  --toast-mobile-transform: none;
  --toast-mobile-min-width: auto;
  --toast-mobile-width: 100%;
}