import { Component } from '@angular/core';
import {
  ToastService,
  ToastPosition,
  ToastTheme
} from '../../../../../play-comp-library/src/lib/components/toast/toast-service';
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

interface ToastExample {
  title: string;
  description: string;
  buttonLabel: string;
  buttonVariant: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  action: () => void;
}

@Component({
  selector: 'app-app-toast',
  imports: [ButtonComponent, CommonModule, FormsModule],
  providers: [ToastService],
  templateUrl: './app-toast.component.html',
  styleUrl: './app-toast.component.scss'
})
export class AppToastComponent {
  // Configuration options
  selectedPosition: ToastPosition = 'top-right';
  selectedTheme: ToastTheme = 'light';
  duration = 4000;
  customTitle = '';
  customMessage = '';

  positions: { value: ToastPosition; label: string }[] = [
    { value: 'top-left', label: 'Top Left' },
    { value: 'top-center', label: 'Top Center' },
    { value: 'top-right', label: 'Top Right' },
    { value: 'bottom-left', label: 'Bottom Left' },
    { value: 'bottom-center', label: 'Bottom Center' },
    { value: 'bottom-right', label: 'Bottom Right' }
  ];

  themes: { value: ToastTheme; label: string }[] = [
    { value: 'light', label: 'Light' },
    { value: 'dark', label: 'Dark' }
  ];

  toastExamples: ToastExample[] = [
    {
      title: 'Success Toast',
      description: 'Show a success message with checkmark icon',
      buttonLabel: 'Success',
      buttonVariant: 'success',
      action: () => this.showSuccessToast()
    },
    {
      title: 'Error Toast',
      description: 'Display error messages with retry option',
      buttonLabel: 'Error',
      buttonVariant: 'danger',
      action: () => this.showErrorToast()
    },
    {
      title: 'Warning Toast',
      description: 'Show warning messages with action button',
      buttonLabel: 'Warning',
      buttonVariant: 'warning',
      action: () => this.showWarningToast()
    },
    {
      title: 'Info Toast',
      description: 'Display informational messages',
      buttonLabel: 'Info',
      buttonVariant: 'primary',
      action: () => this.showInfoToast()
    },
    {
      title: 'Loading Toast',
      description: 'Show loading state with spinner',
      buttonLabel: 'Loading',
      buttonVariant: 'secondary',
      action: () => this.showLoadingToast()
    },
    {
      title: 'Promise Toast',
      description: 'Handle async operations with loading/success/error states',
      buttonLabel: 'Promise',
      buttonVariant: 'primary',
      action: () => this.showPromiseToast()
    }
  ];

  constructor(private toastService: ToastService) {}

  onPositionChange() {
    this.toastService.setPosition(this.selectedPosition);
  }

  onThemeChange() {
    this.toastService.setTheme(this.selectedTheme);
  }

  showSuccessToast() {
    this.toastService.success({
      title: 'Success!',
      message: 'Operation completed successfully',
      theme: this.selectedTheme,
      duration: this.duration
    });
  }

  showErrorToast() {
    this.toastService.error({
      title: 'Error!',
      message: 'Something went wrong. Please try again.',
      theme: this.selectedTheme,
      duration: this.duration,
      showRetryButton: true,
      retryButtonText: 'Retry'
    }).then(result => {
      if (result.action === 'retry') {
        this.toastService.info({
          title: 'Retrying...',
          message: 'Attempting to retry the operation',
          theme: this.selectedTheme,
          duration: 2000
        });
      }
    });
  }

  showWarningToast() {
    this.toastService.warning({
      title: 'Warning!',
      message: 'Please check your input before proceeding',
      theme: this.selectedTheme,
      duration: this.duration,
      showActionButton: true,
      actionButtonText: 'Review'
    }).then(result => {
      if (result.action === 'action') {
        this.toastService.success({
          title: 'Reviewed!',
          message: 'Input has been reviewed',
          theme: this.selectedTheme,
          duration: 2000
        });
      }
    });
  }

  showInfoToast() {
    this.toastService.info({
      title: 'Information',
      message: 'Here is some important information for you',
      theme: this.selectedTheme,
      duration: this.duration,
      showLearnMoreButton: true,
      learnMoreButtonText: 'Learn More'
    }).then(result => {
      if (result.action === 'learn-more') {
        this.toastService.info({
          title: 'More Info',
          message: 'Additional details about this feature',
          theme: this.selectedTheme,
          duration: 3000
        });
      }
    });
  }

  showLoadingToast() {
    this.toastService.loading({
      title: 'Loading...',
      message: 'Please wait while we process your request',
      theme: this.selectedTheme,
      showCancelButton: true,
      cancelButtonText: 'Cancel'
    });

    // Auto-dismiss after 3 seconds for demo
    setTimeout(() => {
      this.toastService.clear();
      this.toastService.success({
        title: 'Completed!',
        message: 'Loading finished successfully',
        theme: this.selectedTheme,
        duration: 2000
      });
    }, 3000);
  }

  showPromiseToast() {
    const mockPromise = new Promise((resolve, reject) => {
      setTimeout(() => {
        Math.random() > 0.5 
          ? resolve({ message: 'Data loaded successfully!' }) 
          : reject({ message: 'Failed to load data' });
      }, 3000);
    });

    this.toastService.promise(mockPromise, {
      loading: 'Loading data...',
      success: 'Success!',
      error: 'Failed!',
      theme: this.selectedTheme,
      duration: 3000
    });
  }

  showCustomToast() {
    if (!this.customTitle && !this.customMessage) {
      this.toastService.warning({
        title: 'Warning',
        message: 'Please enter a title or message',
        theme: this.selectedTheme,
        duration: 3000
      });
      return;
    }

    this.toastService.info({
      title: this.customTitle || undefined,
      message: this.customMessage || undefined,
      theme: this.selectedTheme,
      duration: this.duration
    });
  }

  clearAllToasts() {
    this.toastService.clear();
  }
}
