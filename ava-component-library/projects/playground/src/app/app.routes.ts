import { Routes } from '@angular/router';
import { AppLayoutComponent } from './components/app-layout/app-layout.component';
import { AppButtonComponent } from './components/app-button/app-button.component';
import { AppAccordionComponent } from './components/app-accordion/app-accordion.component';
import { AppTabsComponent } from './components/app-tabs/app-tabs.component';
import { AppTextboxComponent } from './components/app-textbox/app-textbox.component';
import { AppCheckboxComponent } from './components/app-checkbox/app-checkbox.component';
import { AppToggleComponent } from './components/app-toggle/app-toggle.component';
import { AppBadgesComponent } from './components/app-badges/app-badges.component';
import { AppSpinnersComponent } from './components/app-spinners/app-spinners.component';
import { AppStepperComponent } from './components/app-stepper/app-stepper.component';
import { AppIconsComponent } from './components/app-icons/app-icons.component';
import { AppAvatarsComponent } from './components/app-avatars/app-avatars.component';
import { AppCardsComponent } from './components/app-cards/app-cards.component';
import { AppTagsComponent } from './components/app-tags/app-tags.component';
import { AppTextareaComponent } from './components/app-textarea/app-textarea.component';
import { AppAutocompleteComponent } from './components/app-autocomplete/app-autocomplete.component';
import { AppPaginationcontrolsComponent } from './components/app-paginationcontrols/app-paginationcontrols.component';
import { AppActionlinksComponent } from './components/app-actionlinks/app-actionlinks.component';
import { AppDropdownComponent } from './components/app-dropdown/app-dropdown.component';
import { AppRadiobuttonComponent } from './components/app-radiobutton/app-radiobutton.component';
import { AppPopupComponent } from './components/app-popup/app-popup.component';
import { AppDateinputcalendarComponent } from './components/app-dateinputcalendar/app-dateinputcalendar.component';
import { AppSidebarComponent } from './components/app-sidebar/app-sidebar.component';
import { AppTableComponent } from './components/app-table/app-table.component';
import { AppFileuploadComponent } from './components/app-fileupload/app-fileupload.component';
import { AppSnackbarComponent } from './components/app-snackbar/app-snackbar.component';
import { AppSliderComponent } from './components/app-slider/app-slider.component';
import { AppFileAttachPillComponent } from './components/app-file-attach-pill/app-file-attach-pill.component';
import { AppProgressbarComponent } from './components/app-progressbar/app-progressbar.component';
import { AppListComponent } from './components/app-list/app-list.component';
import { AppTooltipComponent } from './components/app-tooltip/app-tooltip.component';
import { AppSkeletonComponent } from './components/app-skeleton/app-skeleton.component';
import { AppTimepickerComponent } from './components/app-timepicker/app-timepicker.component';
import { AppDateTimePickerComponent } from './components/app-date-time-picker/app-date-time-picker.component';
import { AppGridComponent } from './components/app-grid/app-grid.component';

// Grid Demo Components
import { BasicUsageDemoComponent as GridBasicUsageDemoComponent } from './components/app-grid/demo/basic-usage-demo/basic-usage-demo.component';
import { ResponsiveDemoComponent as GridResponsiveDemoComponent } from './components/app-grid/demo/responsive-demo/responsive-demo.component';
import { FlexboxDemoComponent as GridFlexboxDemoComponent } from './components/app-grid/demo/flexbox-demo/flexbox-demo.component';
import { AlignmentDemoComponent as GridAlignmentDemoComponent } from './components/app-grid/demo/alignment-demo/alignment-demo.component';
import { OrderingDemoComponent as GridOrderingDemoComponent } from './components/app-grid/demo/ordering-demo/ordering-demo.component';
import { NestedDemoComponent as GridNestedDemoComponent } from './components/app-grid/demo/nested-demo/nested-demo.component';

// Timepicker Demo Components
import { BasicUsageDemoComponent as TimepickerBasicUsageDemoComponent } from './components/app-timepicker/demo/basic-usage-demo/basic-usage-demo.component';
import { ScrollModeDemoComponent } from './components/app-timepicker/demo/scroll-mode-demo/scroll-mode-demo.component';
import { InlineInputDemoComponent } from './components/app-timepicker/demo/inline-input-demo/inline-input-demo.component';
import { KeyboardNavigationDemoComponent } from './components/app-timepicker/demo/keyboard-navigation-demo/keyboard-navigation-demo.component';
import { ValidationDemoComponent } from './components/app-timepicker/demo/validation-demo/validation-demo.component';
import { CustomStylingDemoComponent as TimepickerCustomStylingDemoComponent } from './components/app-timepicker/demo/custom-styling-demo/custom-styling-demo.component';

// Skeleton Demo Components
import { BasicUsageDemoComponent as SkeletonBasicUsageDemoComponent } from './components/app-skeleton/demo/basic-usage-demo/basic-usage-demo.component';
import { ShapesDemoComponent as SkeletonShapesDemoComponent } from './components/app-skeleton/demo/shapes-demo/shapes-demo.component';
import { AnimationsDemoComponent as SkeletonAnimationsDemoComponent } from './components/app-skeleton/demo/animations-demo/animations-demo.component';
import { CustomStylingDemoComponent as SkeletonCustomStylingDemoComponent } from './components/app-skeleton/demo/custom-styling-demo/custom-styling-demo.component';
import { CardSkeletonDemoComponent } from './components/app-skeleton/demo/card-skeleton-demo/card-skeleton-demo.component';
import { ListSkeletonDemoComponent } from './components/app-skeleton/demo/list-skeleton-demo/list-skeleton-demo.component';

// List Demo Components
import { BasicUsageDemoComponent as ListBasicUsageDemoComponent } from './components/app-list/demo/basic-usage-demo/basic-usage-demo.component';
import { MultiSelectionDemoComponent } from './components/app-list/demo/multi-selection-demo/multi-selection-demo.component';
import { ActionButtonsDemoComponent } from './components/app-list/demo/action-buttons-demo/action-buttons-demo.component';
import { FormIntegrationDemoComponent as ListFormIntegrationDemoComponent } from './components/app-list/demo/form-integration-demo/form-integration-demo.component';
import { AccessibilityDemoComponent as ListAccessibilityDemoComponent } from './components/app-list/demo/accessibility-demo/accessibility-demo.component';
import { AppDividersComponent } from './components/app-dividers/app-dividers.component';
import { AppGlassButtonComponent } from './components/app-glass-button/app-glass-button.component';
import { AppHighContrastTestComponent } from './components/app-high-contrast-test/app-high-contrast-test.component';
import { AppDrawerComponent } from './components/app-drawer/app-drawer.component';

// Drawer Demo Components
import { BasicUsageDemoComponent as DrawerBasicUsageDemoComponent } from './components/app-drawer/demo/basic-usage-demo/basic-usage-demo.component';
import { PositionsDemoComponent as DrawerPositionsDemoComponent } from './components/app-drawer/demo/positions-demo/positions-demo.component';
import { SizesDemoComponent as DrawerSizesDemoComponent } from './components/app-drawer/demo/sizes-demo/sizes-demo.component';
import { BehaviorDemoComponent as DrawerBehaviorDemoComponent } from './components/app-drawer/demo/behavior-demo/behavior-demo.component';
import { ContentDemoComponent as DrawerContentDemoComponent } from './components/app-drawer/demo/content-demo/content-demo.component';
import { ResizableDemoComponent as DrawerResizableDemoComponent } from './components/app-drawer/demo/resizable-demo/resizable-demo.component';

// Composite Components
import { AppTextCardComponent } from './components/app-text-card/app-text-card.component';
import { AppConfirmationPopupComponent } from './components/app-confirmation-popup/app-confirmation-popup.component';
import { AppListCardComponent } from './components/app-list-card/app-list-card.component';
import { AppCustomSidebarComponent } from './components/app-custom-sidebar/app-custom-sidebar.component';
import { AppViewsCardComponent } from './components/app-views-card/app-views-card.component';
import { AppFlipCardComponent } from './components/app-flip-card/app-flip-card.component';
import { AppMenuComponent } from './components/app-menu/app-menu.component';
import { AppNavBarComponent } from './components/app-nav-bar/app-nav-bar.component';
import { BasicUsageDemoComponent as NavBarBasicUsageDemoComponent } from './components/app-nav-bar/demo/basic-usage-demo/basic-usage-demo.component';

// Menu Demo Components
import { BasicUsageDemoComponent as MenuBasicUsageDemoComponent } from './components/app-menu/demo/basic-usage-demo/basic-usage-demo.component';
import { WithIconsDemoComponent } from './components/app-menu/demo/with-icons-demo/with-icons-demo.component';
import { WithDescriptionsDemoComponent } from './components/app-menu/demo/with-descriptions-demo/with-descriptions-demo.component';
import { PositioningDemoComponent } from './components/app-menu/demo/positioning-demo/positioning-demo.component';
import { MultiColumnDemoComponent } from './components/app-menu/demo/multi-column-demo/multi-column-demo.component';
import { DisabledItemsDemoComponent } from './components/app-menu/demo/disabled-items-demo/disabled-items-demo.component';

import { AppSearchFilterPanelComponent } from './components/app-search-filter-panel/app-search-filter-panel.component';
import { SearchFilterPanelBasicUsageDemoComponent } from './components/app-search-filter-panel/demo/basic-usage-demo/basic-usage-demo.component';
import { SearchFilterPanelHorizontalDemoComponent } from './components/app-search-filter-panel/demo/horizontal-demo/horizontal-demo.component';
import { AppDataTableWithActionsComponent } from './components/app-data-table-with-actions/app-data-table-with-actions.component';
import { AppDashboardWidgetGridComponent } from './components/app-dashboard-widget-grid/app-dashboard-widget-grid.component';
import { AppMultiStepFormWizardComponent } from './components/app-multi-step-form-wizard/app-multi-step-form-wizard.component';
import { BasicUsageDemoComponent as MultiStepFormWizardBasicUsageDemoComponent } from './components/app-multi-step-form-wizard/demo/basic-usage-demo/basic-usage-demo.component';

// Card Demo Components
import { BasicUsageDemoComponent as CardBasicUsageDemoComponent } from './components/app-cards/demo/basic-usage-demo/basic-usage-demo.component';
import { WithHeaderDemoComponent as CardWithHeaderDemoComponent } from './components/app-cards/demo/with-header-demo/with-header-demo.component';
import { WithFooterDemoComponent as CardWithFooterDemoComponent } from './components/app-cards/demo/with-footer-demo/with-footer-demo.component';
import { WithActionsDemoComponent as CardWithActionsDemoComponent } from './components/app-cards/demo/with-actions-demo/with-actions-demo.component';
import { ComplexDemoComponent as CardComplexDemoComponent } from './components/app-cards/demo/complex-demo/complex-demo.component';
import { AppUserProfileCardComponent } from './components/app-user-profile-card/app-user-profile-card.component';
import { AppLoginComponent } from './components/app-login/app-login.component';
import { AppChatWindowComponent } from './components/app-chat-window/app-chat-window.component';

// Dividers Demo Components
import { BasicUsageDemoComponent as DividersBasicUsageDemoComponent } from './components/app-dividers/demo/basic-usage-demo/basic-usage-demo.component';
import { VariantsDemoComponent as DividersVariantsDemoComponent } from './components/app-dividers/demo/variants-demo/variants-demo.component';
import { OrientationDemoComponent as DividersOrientationDemoComponent } from './components/app-dividers/demo/orientation-demo/orientation-demo.component';
import { ApiDemoComponent as DividersApiDemoComponent } from './components/app-dividers/demo/api-demo/api-demo.component';

// Spinner Demo Components
import { BasicUsageDemoComponent as SpinnerBasicUsageDemoComponent } from './components/app-spinners/demo/basic-usage-demo/basic-usage-demo.component';
import { TypesDemoComponent } from './components/app-spinners/demo/types-demo/types-demo.component';
import { SizesDemoComponent as SpinnerSizesDemoComponent } from './components/app-spinners/demo/sizes-demo/sizes-demo.component';
import { ColorsDemoComponent } from './components/app-spinners/demo/colors-demo/colors-demo.component';
import { ApiDemoComponent as SpinnerApiDemoComponent } from './components/app-spinners/demo/api-demo/api-demo.component';

// Button Demo Components
import { BasicUsageDemoComponent } from './components/app-button/demo/basic-usage-demo/basic-usage-demo.component';
import { VariantsDemoComponent } from './components/app-button/demo/variants-demo/variants-demo.component';
import { SizesDemoComponent } from './components/app-button/demo/sizes-demo/sizes-demo.component';
import { GlassDemoComponent } from './components/app-button/demo/glass-demo/glass-demo.component';
import { HoverEffectsDemoComponent } from './components/app-button/demo/hover-effects-demo/hover-effects-demo.component';
import { PressedEffectsDemoComponent } from './components/app-button/demo/pressed-effects-demo/pressed-effects-demo.component';
import { IconsDemoComponent } from './components/app-button/demo/icons-demo/icons-demo.component';
import { StatesDemoComponent } from './components/app-button/demo/states-demo/states-demo.component';
import { ShapesDemoComponent } from './components/app-button/demo/shapes-demo/shapes-demo.component';
import { ApiDemoComponent } from './components/app-button/demo/api-demo/api-demo.component';
import { ButtonEventsDemoComponent } from './components/app-button/demo/events/events';
import { ButtonMatrixDemoComponent } from './components/app-button/demo/button-matrix-demo/button-matrix-demo.component';

// Checkbox Demo Components
import { BasicUsageDemoComponent as CheckboxBasicUsageDemoComponent } from './components/app-checkbox/demo/basic-usage-demo/basic-usage-demo.component';
import { VariantsDemoComponent as CheckboxVariantsDemoComponent } from './components/app-checkbox/demo/variants-demo/variants-demo.component';
import { SizesDemoComponent as CheckboxSizesDemoComponent } from './components/app-checkbox/demo/sizes-demo/sizes-demo.component';
import { StatesDemoComponent as CheckboxStatesDemoComponent } from './components/app-checkbox/demo/states-demo/states-demo.component';
import { IndeterminateDemoComponent } from './components/app-checkbox/demo/indeterminate-demo/indeterminate-demo.component';
import { AccessibilityDemoComponent } from './components/app-checkbox/demo/accessibility-demo/accessibility-demo.component';
import { EventsDemoComponent } from './components/app-checkbox/demo/events-demo/events-demo.component';
import { ApiDemoComponent as CheckboxApiDemoComponent } from './components/app-checkbox/demo/api-demo/api-demo.component';
import { OrientationsDemoComponent as CheckboxOrientationsDemoComponent } from './components/app-checkbox/demo/orientations-demo/orientations-demo.component';

// Radio Button Demo Components
import { BasicUsageDemoComponent as RadioBasicUsageDemoComponent } from './components/app-radiobutton/demo/basic-usage-demo/basic-usage-demo.component';
import { OrientationsDemoComponent } from './components/app-radiobutton/demo/orientations-demo/orientations-demo.component';
import { SizesDemoComponent as RadioSizesDemoComponent } from './components/app-radiobutton/demo/sizes-demo/sizes-demo.component';
import { CustomColorsDemoComponent } from './components/app-radiobutton/demo/custom-colors-demo/custom-colors-demo.component';
import { AnimationsDemoComponent } from './components/app-radiobutton/demo/animations-demo/animations-demo.component';
import { StatesDemoComponent as RadioStatesDemoComponent } from './components/app-radiobutton/demo/states-demo/states-demo.component';
import { FormIntegrationDemoComponent } from './components/app-radiobutton/demo/form-integration-demo/form-integration-demo.component';
import { ApiDemoComponent as RadioApiDemoComponent } from './components/app-radiobutton/demo/api-demo/api-demo.component';

// Slider Demo Components
import { SliderBasicDemoComponent } from './components/app-slider/demo/basic-usage-demo/slider-basic-demo.component';
import { SliderOrientationDemoComponent } from './components/app-slider/demo/orientations-demo/slider-orientation-demo.component';
import { SliderSizesDemoComponent } from './components/app-slider/demo/sizes-demo/slider-sizes-demo.component';
import { SliderStatesDemoComponent } from './components/app-slider/demo/states-demo/slider-states-demo.component';
import { SliderApiDemoComponent } from './components/app-slider/demo/api-demo/slider-api-demo.component';
import { MultiRangeDemoComponent } from './components/app-slider/demo/multi-range-demo/multi-range-demo.component';
import { IconThumbDemoComponent } from './components/app-slider/demo/icon-thumb-demo/icon-thumb-demo.component';

// Toggle Demo Components
import { BasicUsageDemoComponent as ToggleBasicUsageDemoComponent } from './components/app-toggle/demo/basic-usage-demo/basic-usage-demo.component';
import { SizesDemoComponent as ToggleSizesDemoComponent } from './components/app-toggle/demo/sizes-demo/sizes-demo.component';
import { PositionsDemoComponent } from './components/app-toggle/demo/positions-demo/positions-demo.component';
import { StatesDemoComponent as ToggleStatesDemoComponent } from './components/app-toggle/demo/states-demo/states-demo.component';
import { AnimationDemoComponent } from './components/app-toggle/demo/animation-demo/animation-demo.component';
import { EventsDemoComponent as ToggleEventsDemoComponent } from './components/app-toggle/demo/events-demo/events-demo.component';
import { FormsDemoComponent } from './components/app-toggle/demo/forms-demo/forms-demo.component';
import { AccessibilityDemoComponent as ToggleAccessibilityDemoComponent } from './components/app-toggle/demo/accessibility-demo/accessibility-demo.component';
import { ApiDemoComponent as ToggleApiDemoComponent } from './components/app-toggle/demo/api-demo/api-demo.component';
import { IconDemoComponent as ToggleIconDemoComponent } from './components/app-toggle/demo/icon-demo/icon-demo.component';

// Stepper Demo Components
import { BasicUsageDemoComponent as StepperBasicUsageDemoComponent } from './components/app-stepper/demo/basic-usage-demo/basic-usage-demo.component';
import { SizesDemoComponent as StepperSizesDemoComponent } from './components/app-stepper/demo/sizes-demo/sizes-demo.component';
import { OrientationDemoComponent } from './components/app-stepper/demo/orientation-demo/orientation-demo.component';
import { InteractiveDemoComponent } from './components/app-stepper/demo/interactive-demo/interactive-demo.component';
import { EventsDemoComponent as StepperEventsDemoComponent } from './components/app-stepper/demo/events-demo/events-demo.component';
import { FormsDemoComponent as StepperFormsDemoComponent } from './components/app-stepper/demo/forms-demo/forms-demo.component';
import { AnimationDemoComponent as StepperAnimationDemoComponent } from './components/app-stepper/demo/animation-demo/animation-demo.component';
import { AccessibilityDemoComponent as StepperAccessibilityDemoComponent } from './components/app-stepper/demo/accessibility-demo/accessibility-demo.component';
import { ApiDemoComponent as StepperApiDemoComponent } from './components/app-stepper/demo/api-demo/api-demo.component';
import { IconDemoComponent } from './components/app-stepper/demo/icon-demo/icon-demo.component';

// Textarea Demo Components
import { TextareaBasicDemoComponent } from './components/app-textarea/textarea-basic-demo/textarea-basic-demo.component';
import { TextareaSizesDemoComponent } from './components/app-textarea/textarea-sizes-demo/textarea-sizes-demo.component';
import { TextareaVariantsDemoComponent } from './components/app-textarea/textarea-variants-demo/textarea-variants-demo.component';
import { TextareaEventsDemoComponent } from './components/app-textarea/textarea-events-demo/textarea-events-demo.component';
import { TextareaAccessibilityDemoComponent } from './components/app-textarea/textarea-accessibility-demo/textarea-accessibility-demo.component';
import { TextareaApiDemoComponent } from './components/app-textarea/textarea-api-demo/textarea-api-demo.component';
import { TextareaIconsDemoComponent } from './components/app-textarea/textarea-icons-demo/textarea-icons-demo.component';

// Textbox Demo Components
import { TextboxBasicUsageComponent } from './components/app-textbox/textbox-basic-usage/textbox-basic-usage.component';
import { TextboxVariantsComponent } from './components/app-textbox/textbox-variants/textbox-variants.component';
import { TextboxSizesComponent } from './components/app-textbox/textbox-sizes/textbox-sizes.component';
import { TextboxIconsAffixesComponent } from './components/app-textbox/textbox-icons-affixes/textbox-icons-affixes.component';
import { TextboxStatesValidationComponent } from './components/app-textbox/textbox-states-validation/textbox-states-validation.component';
import { TextboxProcessingEffectsComponent } from './components/app-textbox/textbox-processing-effects/textbox-processing-effects.component';
import { TextboxFormIntegrationComponent } from './components/app-textbox/textbox-form-integration/textbox-form-integration.component';
import { TextboxGlassEffectsComponent } from './components/app-textbox/textbox-glass-effects/textbox-glass-effects.component';

// Accordion Demo Components
import { AccordionBasicUsageComponent } from './components/app-accordion/accordion-basic-usage/accordion-basic-usage.component';
import { AccordionTypesComponent } from './components/app-accordion/accordion-types/accordion-types.component';
import { AccordionIconsComponent } from './components/app-accordion/accordion-icons/accordion-icons.component';
import { AccordionAnimationComponent } from './components/app-accordion/accordion-animation/accordion-animation.component';
import { AccordionControlledComponent } from './components/app-accordion/accordion-controlled/accordion-controlled.component';
import { AccordionPositionsComponent } from './components/app-accordion/accordion-positions/accordion-positions.component';
import { AccordionAccessibilityComponent } from './components/app-accordion/accordion-accessibility/accordion-accessibility.component';
import { AccordionApiComponent } from './components/app-accordion/accordion-api/accordion-api.component';

// Badges Demo Components
import { BadgesBasicUsageComponent } from './components/app-badges/badges-basic-usage/badges-basic-usage.component';
import { BadgesSizesComponent } from './components/app-badges/badges-sizes/badges-sizes.component';

// Action Links Demo Components
import { ActionlinksBasicUsageComponent } from './components/app-actionlinks/actionlinks-basic-usage/actionlinks-basic-usage.component';
import { ActionlinksColorsComponent } from './components/app-actionlinks/actionlinks-colors/actionlinks-colors.component';
import { ActionlinksSizesComponent } from './components/app-actionlinks/actionlinks-sizes/actionlinks-sizes.component';
import { ActionlinksUnderlineComponent } from './components/app-actionlinks/actionlinks-underline/actionlinks-underline.component';
import { ActionlinksCustomColorsComponent } from './components/app-actionlinks/actionlinks-custom-colors/actionlinks-custom-colors.component';
import { ActionlinksAccessibilityComponent } from './components/app-actionlinks/actionlinks-accessibility/actionlinks-accessibility.component';
import { ActionlinksApiComponent } from './components/app-actionlinks/actionlinks-api/actionlinks-api.component';

// Dropdown Demo Components
import { DropdownBasicUsageComponent } from './components/app-dropdown/dropdown-basic-usage/dropdown-basic-usage.component';
import { DropdownDefaultSelectionComponent } from './components/app-dropdown/dropdown-default-selection/dropdown-default-selection.component';
import { DropdownSearchComponent } from './components/app-dropdown/dropdown-search/dropdown-search.component';
import { DropdownIconsComponent } from './components/app-dropdown/dropdown-icons/dropdown-icons.component';
import { DropdownMultiSelectComponent } from './components/app-dropdown/dropdown-multi-select/dropdown-multi-select.component';
import { DropdownSingleSelectComponent } from './components/app-dropdown/dropdown-single-select/dropdown-single-select.component';
import { DropdownDisabledComponent } from './components/app-dropdown/dropdown-disabled/dropdown-disabled.component';
import { DropdownApiComponent } from './components/app-dropdown/dropdown-api/dropdown-api.component';
import { DropdownNestedComponent } from './components/app-dropdown/dropdown-nested/dropdown-nested.component';

// Tags Demo Components
import { TagsBasicUsageComponent } from './components/app-tags/tags-basic-usage/tags-basic-usage.component';
import { TagsColorsComponent } from './components/app-tags/tags-colors/tags-colors.component';
import { TagsVariantsComponent } from './components/app-tags/tags-variants/tags-variants.component';
import { TagsSizesComponent } from './components/app-tags/tags-sizes/tags-sizes.component';
import { TagsIconsComponent } from './components/app-tags/tags-icons/tags-icons.component';
import { TagsAvatarsComponent } from './components/app-tags/tags-avatars/tags-avatars.component';
import { TagsInteractiveComponent } from './components/app-tags/tags-interactive/tags-interactive.component';
// TODO: Add imports for accessibility, api components when created

// Icons Demo Components
import { IconsBasicUsageComponent } from './components/app-icons/icons-basic-usage/icons-basic-usage.component';
import { IconsSizesComponent } from './components/app-icons/icons-sizes/icons-sizes.component';
import { IconsColorsComponent } from './components/app-icons/icons-colors/icons-colors.component';
import { IconsInteractiveComponent } from './components/app-icons/icons-interactive/icons-interactive.component';
import { IconsStatesComponent } from './components/app-icons/icons-states/icons-states.component';
import { IconsAccessibilityComponent } from './components/app-icons/icons-accessibility/icons-accessibility.component';

// Tabs Demo Components
import { AppTabsBasicUsageDemoComponent } from './components/app-tabs/demo/basic-usage-demo/basic-usage-demo.component';
import { AppTabsVariantsDemoComponent } from './components/app-tabs/demo/variants-demo/variants-demo.component';
import { AppTabsIconsDemoComponent } from './components/app-tabs/demo/icons-demo/icons-demo.component';
import { AppTabsBadgesDemoComponent } from './components/app-tabs/demo/badges-demo/badges-demo.component';
import { AppTabsCloseableDemoComponent } from './components/app-tabs/demo/closeable-demo/closeable-demo.component';
import { AppTabsScrollableDemoComponent } from './components/app-tabs/demo/scrollable-demo/scrollable-demo.component';
import { AppTabsDropdownDemoComponent } from './components/app-tabs/demo/dropdown-demo/dropdown-demo.component';
import { AppTabsVerticalDemoComponent } from './components/app-tabs/demo/vertical-demo/vertical-demo.component';
import { AppTabsCustomStylesDemoComponent } from './components/app-tabs/demo/custom-styles-demo/custom-styles-demo.component';
import { AppCardBasicComponent } from './components/app-cards/demo/card-basic/card-basic.component';
import { AppUserDataTableComponent } from './components/app-user-data-table/app-user-data-table.component';

// Autocomplete Demo Components
import { AppAutocompleteBasicUsageDemoComponent } from './components/app-autocomplete/demo/basic-usage-demo/basic-usage-demo.component';
import { AppAutocompleteMultiSelectDemoComponent } from './components/app-autocomplete/demo/multi-select-demo/multi-select-demo.component';
import { AppAutocompleteIconsDemoComponent } from './components/app-autocomplete/demo/icons-demo/icons-demo.component';
import { AppAutocompleteLoadingDemoComponent } from './components/app-autocomplete/demo/loading-demo/loading-demo.component';
import { AppAutocompleteTemplatesDemoComponent } from './components/app-autocomplete/demo/templates-demo/templates-demo.component';
import { AppAutocompleteFormsDemoComponent } from './components/app-autocomplete/demo/forms-demo/forms-demo.component';
import { AppAutocompleteAccessibilityDemoComponent } from './components/app-autocomplete/demo/accessibility-demo/accessibility-demo.component';
import { AppAutocompleteAsyncDemoComponent } from './components/app-autocomplete/demo/async-demo/async-demo.component';
import { AppAutocompleteCustomStylesDemoComponent } from './components/app-autocomplete/demo/custom-styles-demo/custom-styles-demo.component';

// Pagination Demo Components
import { AppPaginationBasicDemoComponent } from './components/app-paginationcontrols/demo/basic-demo/basic-demo.component';
import { AppPaginationExtendedDemoComponent } from './components/app-paginationcontrols/demo/extended-demo/extended-demo.component';
import { AppPaginationStandardDemoComponent } from './components/app-paginationcontrols/demo/standard-demo/standard-demo.component';
import { AppPaginationPageInfoDemoComponent } from './components/app-paginationcontrols/demo/page-info-demo/page-info-demo.component';
import { AppPaginationSimplePageInfoDemoComponent } from './components/app-paginationcontrols/demo/simple-page-info-demo/simple-page-info-demo.component';
import { AppPaginationVariantsDemoComponent } from './components/app-paginationcontrols/demo/variants-demo/variants-demo.component';
import { AppPaginationAccessibilityDemoComponent } from './components/app-paginationcontrols/demo/accessibility-demo/accessibility-demo.component';

// Avatar Demo Components
import { BasicDemoComponent as AvatarBasicDemoComponent } from './components/app-avatars/demo/basic-demo/basic-demo.component';
import { SizesDemoComponent as AvatarSizesDemoComponent } from './components/app-avatars/demo/sizes-demo/sizes-demo.component';
import { ShapesDemoComponent as AvatarShapesDemoComponent } from './components/app-avatars/demo/shapes-demo/shapes-demo.component';
import { BadgesDemoComponent as AvatarBadgesDemoComponent } from './components/app-avatars/demo/badges-demo/badges-demo.component';
import { TextLabelsDemoComponent as AvatarTextLabelsDemoComponent } from './components/app-avatars/demo/text-labels-demo/text-labels-demo.component';
import { StatesDemoComponent as AvatarStatesDemoComponent } from './components/app-avatars/demo/states-demo/states-demo.component';
import { GradientsDemoComponent as AvatarGradientsDemoComponent } from './components/app-avatars/demo/gradients-demo/gradients-demo.component';
import { AccessibilityDemoComponent as AvatarAccessibilityDemoComponent } from './components/app-avatars/demo/accessibility-demo/accessibility-demo.component';
import { AppThemePreviewComponent } from './components/app-theme-preview/app-theme-preview.component';

// Calendar Demo Components
import { BasicDemoComponent as CalendarBasicDemoComponent } from './components/app-calendar-demo/demo/basic-demo/basic-demo.component';
import { RangeDemoComponent as CalendarRangeDemoComponent } from './components/app-calendar-demo/demo/range-demo/range-demo.component';
import { AlwaysOpenDemoComponent as CalendarAlwaysOpenDemoComponent } from './components/app-calendar-demo/demo/always-open-demo/always-open-demo.component';
import { CustomizationDemoComponent as CalendarCustomizationDemoComponent } from './components/app-calendar-demo/demo/customization-demo/customization-demo.component';
import { SurfaceEffectsDemoComponent as CalendarSurfaceEffectsDemoComponent } from './components/app-calendar-demo/demo/surface-effects-demo/surface-effects-demo.component';
import { KeyboardNavigationDemoComponent as CalendarKeyboardNavigationDemoComponent } from './components/app-calendar-demo/demo/keyboard-navigation-demo/keyboard-navigation-demo.component';
import { AccessibilityDemoComponent as CalendarAccessibilityDemoComponent } from './components/app-calendar-demo/demo/accessibility-demo/accessibility-demo.component';
import { FormsIntegrationDemoComponent as CalendarFormsIntegrationDemoComponent } from './components/app-calendar-demo/demo/forms-integration-demo/forms-integration-demo.component';
import { AppCalendarDemoComponent } from './components/app-calendar-demo/app-calendar-demo.component';

// Tooltip Demo Components
import { TooltipBasicUsageDemoComponent } from './components/app-tooltip/demo/basic-usage-demo/basic-usage-demo.component';
import { TooltipPositionsDemoComponent } from './components/app-tooltip/demo/positions-demo/positions-demo.component';
import { TooltipSizesDemoComponent } from './components/app-tooltip/demo/sizes-demo/sizes-demo.component';
import { TooltipAnimationDemoComponent } from './components/app-tooltip/demo/animation-demo/animation-demo.component';
import { TooltipBehaviorsDemoComponent } from './components/app-tooltip/demo/behaviors-demo/behaviors-demo.component';
import { TooltipVariantsDemoComponent } from './components/app-tooltip/demo/variants-demo/variants-demo.component';
import { TooltipAccessibilityDemoComponent } from './components/app-tooltip/demo/accessibility-demo/accessibility-demo.component';

import { AppImageCardComponent } from './components/app-cards/demo/image-card/image-card.component';

// Snackbar demo imports
import { SnackbarBasicUsageDemoComponent } from './components/app-snackbar/demo/basic-usage-demo/basic-usage-demo.component';
import { SnackbarPositionsDemoComponent } from './components/app-snackbar/demo/positions-demo/positions-demo.component';
import { SnackbarVariantsDemoComponent } from './components/app-snackbar/demo/variants-demo/variants-demo.component';
import { SnackbarActionsIconsDemoComponent } from './components/app-snackbar/demo/actions-icons-demo/actions-icons-demo.component';

// Popup Demo Components
import { PopupBasicUsageDemoComponent } from './components/app-popup/demo/basic-usage-demo.component';
import { PopupTriggersDemoComponent } from './components/app-popup/demo/triggers-demo.component';
import { PopupPositioningDemoComponent } from './components/app-popup/demo/positioning-demo.component';
import { PopupAnimationDemoComponent } from './components/app-popup/demo/animation-demo.component';
import { PopupCustomContentDemoComponent } from './components/app-popup/demo/custom-content-demo.component';
import { PopupAccessibilityDemoComponent } from './components/app-popup/demo/accessibility-demo.component';
import { AppApprovalCardComponent } from './components/app-cards/demo/approval-card/approval-card.component';
import { TextareaCounterVariantComponent } from './components/app-textarea/textarea-counter-variant/textarea-counter-variant.component';
import { AppFooterComponent } from './components/app-footer/app-footer.component';
import { AppRatingCardComponent } from './components/app-cards/demo/rating-card/rating-card.component';
import { AppBreadcrumbsComponent } from './components/app-breadcrumbs/app-breadcrumbs.component';
import { AppDataGridComponent } from './components/app-data-grid/app-data-grid.component';
import { AppCubicalLoadingComponent } from './components/app-cubical-loading/app-cubical-loading.component';
import { AppSelectComponent } from './components/app-select/app-select.component';
import { AppDialogComponent } from './components/app-dialog/app-dialog.component';
import { AppToastComponent } from './components/app-toast/app-toast.component';

export const routes: Routes = [
  // Landing page route
  { path: '', component: AppLayoutComponent },

  // Component routes
  { path: 'app-button', component: AppButtonComponent },
  { path: 'app-accordion', component: AppAccordionComponent },
  { path: 'app-spinners', component: AppSpinnersComponent },
  { path: 'app-checkbox', component: AppCheckboxComponent },
  { path: 'app-toggle', component: AppToggleComponent },
  { path: 'app-icons', component: AppIconsComponent },
  { path: 'app-badges', component: AppBadgesComponent },
  { path: 'app-avatars', component: AppAvatarsComponent },
  { path: 'app-breadcrumbs', component: AppBreadcrumbsComponent },
  { path: 'app-pagination', component: AppPaginationcontrolsComponent },
  { path: 'app-date-input', component: AppDateinputcalendarComponent },
  { path: 'app-calendar-demo', component: AppCalendarDemoComponent },
  { path: 'app-timepicker', component: AppTimepickerComponent },

  // Timepicker Demo Routes
  {
    path: 'app-timepicker/basic-usage',
    component: TimepickerBasicUsageDemoComponent,
  },
  { path: 'app-timepicker/scroll-mode', component: ScrollModeDemoComponent },
  { path: 'app-timepicker/inline-input', component: InlineInputDemoComponent },
  {
    path: 'app-timepicker/keyboard-navigation',
    component: KeyboardNavigationDemoComponent,
  },
  { path: 'app-timepicker/validation', component: ValidationDemoComponent },
  {
    path: 'app-timepicker/custom-styling',
    component: TimepickerCustomStylingDemoComponent,
  },
  { path: 'app-dropdown', component: AppDropdownComponent },
  { path: 'app-tags', component: AppTagsComponent },
  { path: 'app-tabs', component: AppTabsComponent },
  { path: 'app-textbox', component: AppTextboxComponent },
  { path: 'app-textarea', component: AppTextareaComponent },
  { path: 'app-autocomplete', component: AppAutocompleteComponent },
  { path: 'app-radiobutton', component: AppRadiobuttonComponent },
  { path: 'app-links', component: AppActionlinksComponent },
  { path: 'app-stepper', component: AppStepperComponent },
  { path: 'app-table', component: AppTableComponent },
  { path: 'app-popup', component: AppPopupComponent },
  { path: 'app-slider', component: AppSliderComponent },
  { path: 'app-file-attach-pill', component: AppFileAttachPillComponent },
  { path: 'app-progress-bar', component: AppProgressbarComponent },
  { path: 'app-list', component: AppListComponent },
  { path: 'app-dividers', component: AppDividersComponent },
  { path: 'app-tooltip', component: AppTooltipComponent },
  { path: 'app-drawer', component: AppDrawerComponent },
  { path: 'app-flip-card', component: AppFlipCardComponent },
  { path: 'app-menu', component: AppMenuComponent },
  { path: 'app-nav-bar', component: AppNavBarComponent },

  // Menu Demo Routes
  { path: 'menu/basic-usage', component: MenuBasicUsageDemoComponent },
  { path: 'menu/with-icons', component: WithIconsDemoComponent },
  { path: 'menu/with-descriptions', component: WithDescriptionsDemoComponent },
  { path: 'menu/positioning', component: PositioningDemoComponent },
  { path: 'menu/multi-column', component: MultiColumnDemoComponent },
  { path: 'menu/disabled-items', component: DisabledItemsDemoComponent },

  // Nav Bar Demo Routes
  { path: 'nav-bar/basic-usage', component: NavBarBasicUsageDemoComponent },
  { path: 'app-skeleton', component: AppSkeletonComponent },

  // Skeleton Demo Routes
  {
    path: 'app-skeleton/basic-usage',
    component: SkeletonBasicUsageDemoComponent,
  },
  { path: 'app-skeleton/shapes', component: SkeletonShapesDemoComponent },
  {
    path: 'app-skeleton/animations',
    component: SkeletonAnimationsDemoComponent,
  },
  {
    path: 'app-skeleton/custom-styling',
    component: SkeletonCustomStylingDemoComponent,
  },
  { path: 'app-skeleton/card-skeleton', component: CardSkeletonDemoComponent },
  { path: 'app-skeleton/list-skeleton', component: ListSkeletonDemoComponent },

  // Drawer Demo Routes
  { path: 'app-drawer/basic-usage', component: DrawerBasicUsageDemoComponent },
  { path: 'app-drawer/positions', component: DrawerPositionsDemoComponent },
  { path: 'app-drawer/sizes', component: DrawerSizesDemoComponent },
  { path: 'app-drawer/behavior', component: DrawerBehaviorDemoComponent },
  { path: 'app-drawer/content', component: DrawerContentDemoComponent },
  { path: 'app-drawer/resizable', component: DrawerResizableDemoComponent },

  // Grid Demo Routes
  { path: 'app-grid/basic-usage', component: GridBasicUsageDemoComponent },
  { path: 'app-grid/responsive', component: GridResponsiveDemoComponent },
  { path: 'app-grid/flexbox', component: GridFlexboxDemoComponent },
  { path: 'app-grid/alignment', component: GridAlignmentDemoComponent },
  { path: 'app-grid/ordering', component: GridOrderingDemoComponent },
  { path: 'app-grid/nested', component: GridNestedDemoComponent },

  // Spinner Demo Routes
  { path: 'spinners/basic-usage', component: SpinnerBasicUsageDemoComponent },
  { path: 'spinners/types', component: TypesDemoComponent },
  { path: 'spinners/sizes', component: SpinnerSizesDemoComponent },
  { path: 'spinners/colors', component: ColorsDemoComponent },
  { path: 'spinners/api', component: SpinnerApiDemoComponent },

  // Dividers Demo Routes
  { path: 'dividers/basic-usage', component: DividersBasicUsageDemoComponent },
  { path: 'dividers/variants', component: DividersVariantsDemoComponent },
  { path: 'dividers/orientation', component: DividersOrientationDemoComponent },
  { path: 'dividers/api', component: DividersApiDemoComponent },
  { path: 'app-sidebar', component: AppSidebarComponent },
  { path: 'app-file-upload', component: AppFileuploadComponent },
  { path: 'app-app-snackbar', component: AppSnackbarComponent },
  { path: 'app-toast', component: AppToastComponent },
  { path: 'app-glass-button', component: AppGlassButtonComponent },

  // Composite Component Routes
  { path: 'app-approval-card', component: AppApprovalCardComponent },
  { path: 'app-text-card', component: AppTextCardComponent },
  { path: 'app-confirmation-popup', component: AppConfirmationPopupComponent },
  { path: 'app-date-time-picker', component: AppDateTimePickerComponent },
  { path: 'app-grid', component: AppGridComponent },
  { path: 'app-cubical-loading', component: AppCubicalLoadingComponent },
  { path: 'app-list-card', component: AppListCardComponent },
  { path: 'app-custom-sidebar', component: AppCustomSidebarComponent },
  { path: 'app-views-card', component: AppViewsCardComponent },
  { path: 'app-search-filter-panel', component: AppSearchFilterPanelComponent },
  {
    path: 'search-filter-panel/basic-usage',
    component: SearchFilterPanelBasicUsageDemoComponent,
  },
  {
    path: 'search-filter-panel/horizontal',
    component: SearchFilterPanelHorizontalDemoComponent,
  },
  { path: 'app-rating-card', component: AppRatingCardComponent },
  {
    path: 'app-data-table-with-actions',
    component: AppDataTableWithActionsComponent,
  },
  {
    path: 'app-user-data-table',
    component: AppUserDataTableComponent,
  },
  {
    path: 'app-dashboard-widget-grid',
    component: AppDashboardWidgetGridComponent,
  },
  {
    path: 'app-multi-step-form-wizard',
    component: AppMultiStepFormWizardComponent,
  },
  {
    path: 'multi-step-form-wizard/basic-usage',
    component: MultiStepFormWizardBasicUsageDemoComponent,
  },
  // Card Demo Routes
  {
    path: 'card/basic-usage',
    component: CardBasicUsageDemoComponent,
  },
  {
    path: 'card/with-header',
    component: CardWithHeaderDemoComponent,
  },
  {
    path: 'card/with-footer',
    component: CardWithFooterDemoComponent,
  },
  {
    path: 'card/with-actions',
    component: CardWithActionsDemoComponent,
  },
  {
    path: 'card/complex',
    component: CardComplexDemoComponent,
  },
  {
    path: 'app-user-profile-card',
    component: AppUserProfileCardComponent,
  },
  {
    path: 'app-login',
    component: AppLoginComponent,
  },
  {
    path: 'app-chat-window',
    component: AppChatWindowComponent,
  },

  // Button Demo Routes
  { path: 'button/basic-usage', component: BasicUsageDemoComponent },
  { path: 'button/variants', component: VariantsDemoComponent },
  { path: 'button/sizes', component: SizesDemoComponent },
  { path: 'button/glass', component: GlassDemoComponent },
  { path: 'button/hover-effects', component: HoverEffectsDemoComponent },
  { path: 'button/pressed-effects', component: PressedEffectsDemoComponent },
  { path: 'button/icons', component: IconsDemoComponent },
  { path: 'button/states', component: StatesDemoComponent },
  { path: 'button/shapes', component: ShapesDemoComponent },
  { path: 'button/events', component: ButtonEventsDemoComponent },
  { path: 'button/api', component: ApiDemoComponent },
  { path: 'button/matrix', component: ButtonMatrixDemoComponent },

  // Checkbox Demo Routes
  { path: 'checkbox/basic-usage', component: CheckboxBasicUsageDemoComponent },
  { path: 'checkbox/variants', component: CheckboxVariantsDemoComponent },
  { path: 'checkbox/sizes', component: CheckboxSizesDemoComponent },
  { path: 'checkbox/states', component: CheckboxStatesDemoComponent },
  { path: 'checkbox/indeterminate', component: IndeterminateDemoComponent },
  {
    path: 'checkbox/orientations',
    component: CheckboxOrientationsDemoComponent,
  },
  { path: 'checkbox/accessibility', component: AccessibilityDemoComponent },
  { path: 'checkbox/events', component: EventsDemoComponent },
  { path: 'checkbox/api', component: CheckboxApiDemoComponent },

  // Radio Button Demo Routes
  { path: 'radiobutton/basic-usage', component: RadioBasicUsageDemoComponent },
  { path: 'radiobutton/orientations', component: OrientationsDemoComponent },
  { path: 'radiobutton/sizes', component: RadioSizesDemoComponent },
  { path: 'radiobutton/custom-colors', component: CustomColorsDemoComponent },
  { path: 'radiobutton/animations', component: AnimationsDemoComponent },
  { path: 'radiobutton/states', component: RadioStatesDemoComponent },
  {
    path: 'radiobutton/form-integration',
    component: FormIntegrationDemoComponent,
  },
  { path: 'radiobutton/api', component: RadioApiDemoComponent },

  // Slider Demo Routes
  { path: 'slider/basic-usage', component: SliderBasicDemoComponent },
  { path: 'slider/orientations', component: SliderOrientationDemoComponent },
  { path: 'slider/sizes', component: SliderSizesDemoComponent },
  { path: 'slider/states', component: SliderStatesDemoComponent },
  { path: 'slider/multi-range', component: MultiRangeDemoComponent },
  { path: 'slider/icon-thumb', component: IconThumbDemoComponent },
  { path: 'slider/api', component: SliderApiDemoComponent },

  // Toggle Demo Routes
  { path: 'toggle/basic-usage', component: ToggleBasicUsageDemoComponent },
  { path: 'toggle/sizes', component: ToggleSizesDemoComponent },
  { path: 'toggle/positions', component: PositionsDemoComponent },
  { path: 'toggle/states', component: ToggleStatesDemoComponent },
  { path: 'toggle/animation', component: AnimationDemoComponent },
  { path: 'toggle/events', component: ToggleEventsDemoComponent },
  { path: 'toggle/forms', component: FormsDemoComponent },
  { path: 'toggle/accessibility', component: ToggleAccessibilityDemoComponent },
  { path: 'toggle/api', component: ToggleApiDemoComponent },
  { path: 'toggle/icon', component: ToggleIconDemoComponent },

  // Stepper Demo Routes
  { path: 'stepper/basic-usage', component: StepperBasicUsageDemoComponent },
  { path: 'stepper/sizes', component: StepperSizesDemoComponent },
  { path: 'stepper/orientation', component: OrientationDemoComponent },
  { path: 'stepper/interactive', component: InteractiveDemoComponent },
  { path: 'stepper/events', component: StepperEventsDemoComponent },
  { path: 'stepper/forms', component: StepperFormsDemoComponent },
  { path: 'stepper/animation', component: StepperAnimationDemoComponent },
  {
    path: 'stepper/accessibility',
    component: StepperAccessibilityDemoComponent,
  },
  { path: 'stepper/api', component: StepperApiDemoComponent },
  { path: 'stepper/icon', component: IconDemoComponent },

  // Textarea Demo Routes
  { path: 'textarea/basic-usage', component: TextareaBasicDemoComponent },
  { path: 'textarea/sizes', component: TextareaSizesDemoComponent },
  { path: 'textarea/variants', component: TextareaVariantsDemoComponent },
  { path: 'textarea/events', component: TextareaEventsDemoComponent },
  { path: 'textarea/icons', component: TextareaIconsDemoComponent },
  {
    path: 'textarea/accessibility',
    component: TextareaAccessibilityDemoComponent,
  },
  { path: 'app-textarea-counter', component: TextareaCounterVariantComponent },
  { path: 'textarea/api', component: TextareaApiDemoComponent },

  // Textbox Demo Routes
  { path: 'textbox/basic-usage', component: TextboxBasicUsageComponent },
  { path: 'textbox/variants', component: TextboxVariantsComponent },
  { path: 'textbox/sizes', component: TextboxSizesComponent },
  { path: 'textbox/icons-affixes', component: TextboxIconsAffixesComponent },
  {
    path: 'textbox/states-validation',
    component: TextboxStatesValidationComponent,
  },
  {
    path: 'textbox/processing-effects',
    component: TextboxProcessingEffectsComponent,
  },
  {
    path: 'textbox/form-integration',
    component: TextboxFormIntegrationComponent,
  },
  { path: 'textbox/glass-effects', component: TextboxGlassEffectsComponent },

  // Accordion Demo Routes
  { path: 'accordion/basic-usage', component: AccordionBasicUsageComponent },
  { path: 'accordion/types', component: AccordionTypesComponent },
  { path: 'accordion/icons', component: AccordionIconsComponent },
  { path: 'accordion/animation', component: AccordionAnimationComponent },
  { path: 'accordion/controlled', component: AccordionControlledComponent },
  { path: 'accordion/positions', component: AccordionPositionsComponent },
  {
    path: 'accordion/accessibility',
    component: AccordionAccessibilityComponent,
  },
  { path: 'accordion/api', component: AccordionApiComponent },

  // Badges Demo Routes
  { path: 'badges/basic-usage', component: BadgesBasicUsageComponent },
  { path: 'badges/sizes', component: BadgesSizesComponent },

  // Action Links Demo Routes
  {
    path: 'action-links/basic-usage',
    component: ActionlinksBasicUsageComponent,
  },
  { path: 'action-links/colors', component: ActionlinksColorsComponent },
  { path: 'action-links/sizes', component: ActionlinksSizesComponent },
  { path: 'action-links/underline', component: ActionlinksUnderlineComponent },
  {
    path: 'action-links/custom-colors',
    component: ActionlinksCustomColorsComponent,
  },
  {
    path: 'action-links/accessibility',
    component: ActionlinksAccessibilityComponent,
  },
  { path: 'action-links/api', component: ActionlinksApiComponent },

  // Dropdown Demo Routes
  { path: 'dropdown/basic-usage', component: DropdownBasicUsageComponent },
  {
    path: 'dropdown/default-selection',
    component: DropdownDefaultSelectionComponent,
  },
  { path: 'dropdown/search', component: DropdownSearchComponent },
  { path: 'dropdown/icons', component: DropdownIconsComponent },
  { path: 'dropdown/multi-select', component: DropdownMultiSelectComponent },
  { path: 'dropdown/single-select', component: DropdownSingleSelectComponent },
  { path: 'dropdown/disabled', component: DropdownDisabledComponent },
  { path: 'dropdown/nested', component: DropdownNestedComponent },
  { path: 'dropdown/api', component: DropdownApiComponent },

  // Tags Demo Routes
  {
    path: 'tags',
    component: AppTagsComponent,
  },
  { path: 'tags/basic-usage', component: TagsBasicUsageComponent },
  { path: 'tags/colors', component: TagsColorsComponent },
  { path: 'tags/variants', component: TagsVariantsComponent },
  { path: 'tags/sizes', component: TagsSizesComponent },
  { path: 'tags/icons', component: TagsIconsComponent },
  { path: 'tags/avatars', component: TagsAvatarsComponent },
  { path: 'tags/interactive', component: TagsInteractiveComponent },
  // { path: 'tags/accessibility', component: TagsAccessibilityComponent },
  // { path: 'tags/api', component: TagsApiComponent },

  // Icons Demo Routes
  { path: 'icons/basic-usage', component: IconsBasicUsageComponent },
  { path: 'icons/sizes', component: IconsSizesComponent },
  { path: 'icons/colors', component: IconsColorsComponent },
  { path: 'icons/interactive', component: IconsInteractiveComponent },
  { path: 'icons/states', component: IconsStatesComponent },
  { path: 'icons/accessibility', component: IconsAccessibilityComponent },

  // Tabs Demo Routes
  { path: 'tabs/basic', component: AppTabsBasicUsageDemoComponent },
  { path: 'tabs/variants', component: AppTabsVariantsDemoComponent },
  { path: 'tabs/icons', component: AppTabsIconsDemoComponent },
  { path: 'tabs/badges', component: AppTabsBadgesDemoComponent },
  { path: 'tabs/closeable', component: AppTabsCloseableDemoComponent },
  { path: 'tabs/scrollable', component: AppTabsScrollableDemoComponent },
  { path: 'tabs/dropdown', component: AppTabsDropdownDemoComponent },
  { path: 'tabs/vertical', component: AppTabsVerticalDemoComponent },
  { path: 'tabs/custom-styles', component: AppTabsCustomStylesDemoComponent },

  //Cards

  {
    path: 'cards',
    component: AppCardsComponent,
  },

  {
    path: 'cards/text-card',
    component: AppCardBasicComponent,
  },
  {
    path: 'cards/image-card',
    component: AppImageCardComponent,
  },
  {
    path: 'cards/approval-card',
    component: AppApprovalCardComponent,
  },

  // Autocomplete Demo Routes
  {
    path: 'autocomplete/basic-usage',
    component: AppAutocompleteBasicUsageDemoComponent,
  },
  {
    path: 'autocomplete/multi-select',
    component: AppAutocompleteMultiSelectDemoComponent,
  },
  { path: 'autocomplete/icons', component: AppAutocompleteIconsDemoComponent },
  {
    path: 'autocomplete/loading',
    component: AppAutocompleteLoadingDemoComponent,
  },
  {
    path: 'autocomplete/templates',
    component: AppAutocompleteTemplatesDemoComponent,
  },
  { path: 'autocomplete/forms', component: AppAutocompleteFormsDemoComponent },
  {
    path: 'autocomplete/accessibility',
    component: AppAutocompleteAccessibilityDemoComponent,
  },
  { path: 'autocomplete/async', component: AppAutocompleteAsyncDemoComponent },
  {
    path: 'autocomplete/custom-styles',
    component: AppAutocompleteCustomStylesDemoComponent,
  },

  // Pagination Demo Routes
  {
    path: 'pagination/basic',
    component: AppPaginationBasicDemoComponent,
  },
  {
    path: 'pagination/extended',
    component: AppPaginationExtendedDemoComponent,
  },
  {
    path: 'pagination/standard',
    component: AppPaginationStandardDemoComponent,
  },
  {
    path: 'pagination/page-info',
    component: AppPaginationPageInfoDemoComponent,
  },
  {
    path: 'pagination/simple-page-info',
    component: AppPaginationSimplePageInfoDemoComponent,
  },
  {
    path: 'pagination/variants',
    component: AppPaginationVariantsDemoComponent,
  },
  {
    path: 'pagination/accessibility',
    component: AppPaginationAccessibilityDemoComponent,
  },

  // Avatar Demo Routes
  {
    path: 'avatars/basic',
    component: AvatarBasicDemoComponent,
  },
  {
    path: 'avatars/sizes',
    component: AvatarSizesDemoComponent,
  },
  {
    path: 'avatars/shapes',
    component: AvatarShapesDemoComponent,
  },
  {
    path: 'avatars/badges',
    component: AvatarBadgesDemoComponent,
  },
  {
    path: 'avatars/text-labels',
    component: AvatarTextLabelsDemoComponent,
  },
  {
    path: 'avatars/states',
    component: AvatarStatesDemoComponent,
  },
  {
    path: 'avatars/gradients',
    component: AvatarGradientsDemoComponent,
  },
  {
    path: 'avatars/accessibility',
    component: AvatarAccessibilityDemoComponent,
  },

  // Calendar Demo Routes
  {
    path: 'calendar/basic',
    component: CalendarBasicDemoComponent,
  },
  {
    path: 'calendar/range',
    component: CalendarRangeDemoComponent,
  },
  {
    path: 'calendar/always-open',
    component: CalendarAlwaysOpenDemoComponent,
  },
  {
    path: 'calendar/customization',
    component: CalendarCustomizationDemoComponent,
  },
  {
    path: 'calendar/surface-effects',
    component: CalendarSurfaceEffectsDemoComponent,
  },
  {
    path: 'calendar/keyboard-navigation',
    component: CalendarKeyboardNavigationDemoComponent,
  },
  {
    path: 'calendar/accessibility',
    component: CalendarAccessibilityDemoComponent,
  },
  {
    path: 'calendar/forms-integration',
    component: CalendarFormsIntegrationDemoComponent,
  },

  // List Demo Routes
  { path: 'list/basic-usage', component: ListBasicUsageDemoComponent },
  { path: 'list/multi-selection', component: MultiSelectionDemoComponent },
  { path: 'list/action-buttons', component: ActionButtonsDemoComponent },
  {
    path: 'list/form-integration',
    component: ListFormIntegrationDemoComponent,
  },
  { path: 'list/accessibility', component: ListAccessibilityDemoComponent },

  // Tooltip Demo Routes
  { path: 'tooltip', component: AppTooltipComponent },
  { path: 'tooltip/basic-usage', component: TooltipBasicUsageDemoComponent },
  { path: 'tooltip/positions', component: TooltipPositionsDemoComponent },
  { path: 'tooltip/sizes', component: TooltipSizesDemoComponent },
  { path: 'tooltip/animation', component: TooltipAnimationDemoComponent },
  { path: 'tooltip/behaviors', component: TooltipBehaviorsDemoComponent },
  { path: 'tooltip/variants', component: TooltipVariantsDemoComponent },
  {
    path: 'tooltip/accessibility',
    component: TooltipAccessibilityDemoComponent,
  },

  // Snackbar Demo Routes
  { path: 'snackbar/basic-usage', component: SnackbarBasicUsageDemoComponent },
  { path: 'snackbar/positions', component: SnackbarPositionsDemoComponent },
  { path: 'snackbar/variants', component: SnackbarVariantsDemoComponent },
  {
    path: 'snackbar/actions-icons',
    component: SnackbarActionsIconsDemoComponent,
  },

  // Popup Demo Routes
  { path: 'popup/basic-usage', component: PopupBasicUsageDemoComponent },
  { path: 'popup/triggers', component: PopupTriggersDemoComponent },
  { path: 'popup/positioning', component: PopupPositioningDemoComponent },
  { path: 'popup/animation', component: PopupAnimationDemoComponent },
  { path: 'popup/custom-content', component: PopupCustomContentDemoComponent },
  { path: 'popup/accessibility', component: PopupAccessibilityDemoComponent },
  //Footer
  { path: 'footer', component: AppFooterComponent },
  { path: 'data-grid', component: AppDataGridComponent },
  { path: 'select', component: AppSelectComponent },
  { path: 'dialog', component: AppDialogComponent },

  // Theme Demo Route
  { path: 'theme-demo', component: AppThemePreviewComponent },

  // Accessibility Testing Route
  { path: 'high-contrast-test', component: AppHighContrastTestComponent },
];
